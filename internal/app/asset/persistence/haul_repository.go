package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"

	"gorm.io/gorm"
)

type haulRepository struct{}

func NewHaulRepository() repository.HaulRepository {
	return &haulRepository{}
}

// Hauling CRUD operations
func (r *haulRepository) CreateHauling(ctx context.Context, dB database.DBI, hauling *models.Hauling) error {
	return dB.GetTx().Create(hauling).Error
}

func (r *haulRepository) GetHauling(ctx context.Context, dB database.DBI, cond models.HaulingCondition) (*models.Hauling, error) {
	var hauling models.Hauling
	query := dB.GetTx().Model(&hauling)

	enrichHaulingQueryWithWhere(query, cond.Where)
	enrichHaulingQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.First(&hauling).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("hauling")
		}
		return nil, err
	}

	return &hauling, nil
}

func (r *haulRepository) GetHaulings(ctx context.Context, dB database.DBI, cond models.HaulingCondition) ([]models.Hauling, error) {
	var haulings []models.Hauling
	query := dB.GetTx().Model(&haulings)

	enrichHaulingQueryWithWhere(query, cond.Where)
	enrichHaulingQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.Find(&haulings).Error
	return haulings, err
}

func (r *haulRepository) GetHaulingList(ctx context.Context, dB database.DBI, param models.GetHaulingListParam) (int, []models.Hauling, error) {
	var haulings []models.Hauling
	var totalRecords int64

	query := dB.GetTx().Model(&haulings)

	// Apply filters
	if param.ClientID != "" {
		query = query.Where("client_id = ?", param.ClientID)
	}

	if param.AssetID != "" {
		query = query.Where("asset_id = ?", param.AssetID)
	}

	if param.OperatorUserID != "" {
		query = query.Where("operator_user_id = ?", param.OperatorUserID)
	}

	if param.HaulStatusCode != "" {
		query = query.Where("haul_status_code = ?", param.HaulStatusCode)
	}

	if param.HaulSubStatusCode != "" {
		query = query.Where("haul_sub_status_code = ?", param.HaulSubStatusCode)
	}

	if param.IsActive != nil {
		if *param.IsActive {
			query = query.Where("end_time IS NULL")
		} else {
			query = query.Where("end_time IS NOT NULL")
		}
	}

	if param.StartDate != nil {
		startDate, err := time.Parse("2006-01-02", *param.StartDate)
		if err == nil {
			query = query.Where("start_time >= ?", startDate)
		}
	}

	if param.EndDate != nil {
		endDate, err := time.Parse("2006-01-02", *param.EndDate)
		if err == nil {
			query = query.Where("start_time <= ?", endDate.Add(24*time.Hour))
		}
	}

	if param.SearchKeyword != "" {
		query = query.Joins("LEFT JOIN ams_assets ON ams_haulings.asset_id = ams_assets.id").
			Where("LOWER(ams_assets.name) LIKE LOWER(?) OR LOWER(ams_assets.serial_number) LIKE LOWER(?)",
				"%"+param.SearchKeyword+"%", "%"+param.SearchKeyword+"%")
	}

	// Count total records
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), haulings, nil
	}

	// Apply ordering
	if param.OrderBy.StartTime != "" {
		query = query.Order("start_time " + param.OrderBy.StartTime)
	} else if param.OrderBy.UpdatedAt != "" {
		query = query.Order("updated_at " + param.OrderBy.UpdatedAt)
	} else {
		query = query.Order("updated_at DESC")
	}

	// Apply pagination
	offset := (param.PageNo - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)

	// Apply preloads
	query = query.Preload("Asset").Preload("HaulStatus").Preload("HaulSubStatus")

	err = query.Find(&haulings).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), haulings, nil
}

func (r *haulRepository) UpdateHauling(ctx context.Context, dB database.DBI, id string, hauling *models.Hauling) error {
	return dB.GetTx().
		Model(&models.Hauling{}).
		Where("id = ?", id).
		Select("*").
		Updates(hauling).
		Error
}

func (r *haulRepository) DeleteHauling(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.Hauling{}, "id = ?", id).Error
}

// Helper functions
func enrichHaulingQueryWithWhere(query *gorm.DB, where models.HaulingWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	}

	if where.OperatorUserID != "" {
		query.Where("operator_user_id = ?", where.OperatorUserID)
	}

	if where.HaulStatusCode != "" {
		query.Where("haul_status_code = ?", where.HaulStatusCode)
	}

	if where.HaulSubStatusCode != "" {
		query.Where("haul_sub_status_code = ?", where.HaulSubStatusCode)
	}

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}

	if where.IsActive != nil {
		if *where.IsActive {
			query.Where("end_time IS NULL")
		} else {
			query.Where("end_time IS NOT NULL")
		}
	}
}

func enrichHaulingQueryWithPreload(query *gorm.DB, preload models.HaulingPreload) {
	if preload.HaulStatus {
		query.Preload("HaulStatus")
	}

	if preload.HaulSubStatus {
		query.Preload("HaulSubStatus")
	}
}

// Haul Status operations
func (r *haulRepository) GetHaulStatus(ctx context.Context, dB database.DBI, cond models.HaulStatusCondition) (*models.HaulStatus, error) {
	var haulStatus models.HaulStatus
	query := dB.GetTx().Model(&haulStatus)

	enrichHaulStatusQueryWithWhere(query, cond.Where)
	enrichHaulStatusQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.First(&haulStatus).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("haul status")
		}
		return nil, err
	}

	return &haulStatus, nil
}

func (r *haulRepository) GetHaulStatuses(ctx context.Context, dB database.DBI, cond models.HaulStatusCondition) ([]models.HaulStatus, error) {
	var haulStatuses []models.HaulStatus
	query := dB.GetTx().Model(&haulStatuses)

	enrichHaulStatusQueryWithWhere(query, cond.Where)
	enrichHaulStatusQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.Find(&haulStatuses).Error
	return haulStatuses, err
}

func (r *haulRepository) GetHaulSubStatus(ctx context.Context, dB database.DBI, cond models.HaulSubStatusCondition) (*models.HaulSubStatus, error) {
	var haulSubStatus models.HaulSubStatus
	query := dB.GetTx().Model(&haulSubStatus)

	enrichHaulSubStatusQueryWithWhere(query, cond.Where)
	enrichHaulSubStatusQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.First(&haulSubStatus).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("haul sub status")
		}
		return nil, err
	}

	return &haulSubStatus, nil
}

func (r *haulRepository) GetHaulSubStatuses(ctx context.Context, dB database.DBI, cond models.HaulSubStatusCondition) ([]models.HaulSubStatus, error) {
	var haulSubStatuses []models.HaulSubStatus
	query := dB.GetTx().Model(&haulSubStatuses)

	enrichHaulSubStatusQueryWithWhere(query, cond.Where)
	enrichHaulSubStatusQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query = query.Select(cond.Columns)
	}

	err := query.Find(&haulSubStatuses).Error
	return haulSubStatuses, err
}

// Helper functions for status queries
func enrichHaulStatusQueryWithWhere(query *gorm.DB, where models.HaulStatusWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}

	if len(where.Codes) > 0 {
		query.Where("code IN ?", where.Codes)
	}
}

func enrichHaulStatusQueryWithPreload(query *gorm.DB, preload models.HaulStatusPreload) {
	if preload.HaulSubStatuses {
		query.Preload("HaulSubStatuses")
	}
}

func enrichHaulSubStatusQueryWithWhere(query *gorm.DB, where models.HaulSubStatusWhere) {
	if where.Code != "" {
		query.Where("code = ?", where.Code)
	}

	if len(where.Codes) > 0 {
		query.Where("code IN ?", where.Codes)
	}

	if where.MainStatusCode != "" {
		query.Where("main_status_code = ?", where.MainStatusCode)
	}

	if where.IsLoad != nil {
		query.Where("is_load = ?", *where.IsLoad)
	}
}

func enrichHaulSubStatusQueryWithPreload(query *gorm.DB, preload models.HaulSubStatusPreload) {
	if preload.MainStatus {
		query.Preload("MainStatus")
	}
}

// Statistics and analytics
func (r *haulRepository) GetHaulingStats(ctx context.Context, dB database.DBI, clientID string, startDate, endDate *string) (*models.HaulingStats, error) {
	var stats models.HaulingStats
	query := dB.GetTx().Model(&models.Hauling{})

	if clientID != "" {
		query = query.Where("client_id = ?", clientID)
	}

	if startDate != nil {
		start, err := time.Parse("2006-01-02", *startDate)
		if err == nil {
			query = query.Where("start_time >= ?", start)
		}
	}

	if endDate != nil {
		end, err := time.Parse("2006-01-02", *endDate)
		if err == nil {
			query = query.Where("start_time <= ?", end.Add(24*time.Hour))
		}
	}

	// Total haulings
	err := query.Count(&stats.TotalHaulings).Error
	if err != nil {
		return nil, err
	}

	// Active haulings (end_time is null)
	err = query.Where("end_time IS NULL").Count(&stats.ActiveHaulings).Error
	if err != nil {
		return nil, err
	}

	// Completed haulings (end_time is not null)
	err = query.Where("end_time IS NOT NULL").Count(&stats.CompletedHaulings).Error
	if err != nil {
		return nil, err
	}

	// By status
	var statusStats []models.HaulingStatusStats
	err = query.Select("haul_status_code as status_code, COUNT(*) as count").
		Group("haul_status_code").
		Find(&statusStats).Error
	if err != nil {
		return nil, err
	}

	stats.ByStatus = statusStats

	return &stats, nil
}

func (r *haulRepository) GetActiveHaulingsByAsset(ctx context.Context, dB database.DBI, assetID string, clientID string) ([]models.Hauling, error) {
	var haulings []models.Hauling
	query := dB.GetTx().Model(&haulings).
		Where("asset_id = ? AND client_id = ? AND end_time IS NULL", assetID, clientID).
		Preload("HaulStatus").
		Preload("HaulSubStatus").
		Order("start_time DESC")

	err := query.Find(&haulings).Error
	return haulings, err
}

func (r *haulRepository) GetHaulingsByOperator(ctx context.Context, dB database.DBI, operatorUserID string, clientID string, limit int) ([]models.Hauling, error) {
	var haulings []models.Hauling
	query := dB.GetTx().Model(&haulings).
		Where("operator_user_id = ? AND client_id = ?", operatorUserID, clientID).
		Preload("Asset").
		Preload("HaulStatus").
		Preload("HaulSubStatus").
		Order("start_time DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&haulings).Error
	return haulings, err
}

// Validation helpers
func (r *haulRepository) IsAssetCurrentlyHauling(ctx context.Context, dB database.DBI, assetID string, clientID string) (bool, error) {
	var count int64
	err := dB.GetTx().Model(&models.Hauling{}).
		Where("asset_id = ? AND client_id = ? AND end_time IS NULL", assetID, clientID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *haulRepository) CanTransitionToStatus(ctx context.Context, dB database.DBI, currentSubStatusCode, targetSubStatusCode string) (bool, error) {
	// Check if transition exists in haul sub status cycles table
	var count int64
	err := dB.GetTx().Model(&models.HaulSubStatusCycle{}).
		Where("prev_code = ? AND next_code = ?", currentSubStatusCode, targetSubStatusCode).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
