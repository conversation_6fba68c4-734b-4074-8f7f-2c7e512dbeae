package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

// Request DTOs
type CreateHaulingReq struct {
	AssetID           string `json:"asset_id" binding:"required"`
	OperatorUserID    string `json:"operator_user_id" binding:"required"`
	HaulStatusCode    string `json:"haul_status_code" binding:"required"`
	HaulSubStatusCode string `json:"haul_sub_status_code" binding:"required"`
	StartTime         string `json:"start_time" binding:"required"` // ISO 8601 format
}

type UpdateHaulingReq struct {
	HaulStatusCode    string `json:"haul_status_code,omitempty"`
	HaulSubStatusCode string `json:"haul_sub_status_code,omitempty"`
	EndTime           string `json:"end_time,omitempty"` // ISO 8601 format, set to end hauling
}

type HaulingListReq struct {
	commonmodel.ListRequest
	AssetID           string `form:"asset_id"`
	OperatorUserID    string `form:"operator_user_id"`
	HaulStatusCode    string `form:"haul_status_code"`
	HaulSubStatusCode string `form:"haul_sub_status_code"`
	IsActive          *bool  `form:"is_active"` // Filter for ongoing haulings
	StartDate         string `form:"start_date"` // YYYY-MM-DD format
	EndDate           string `form:"end_date"`   // YYYY-MM-DD format
}

// Response DTOs
type HaulingRes struct {
	ID                string    `json:"id"`
	AssetID           string    `json:"asset_id"`
	OperatorUserID    string    `json:"operator_user_id"`
	HaulStatusCode    string    `json:"haul_status_code"`
	HaulSubStatusCode string    `json:"haul_sub_status_code"`
	StartTime         time.Time `json:"start_time"`
	EndTime           null.Time `json:"end_time"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// Relationships
	Asset         *AssetBasicRes     `json:"asset,omitempty"`
	HaulStatus    *HaulStatusRes     `json:"haul_status,omitempty"`
	HaulSubStatus *HaulSubStatusRes  `json:"haul_sub_status,omitempty"`
	OperatorUser  *UserBasicRes      `json:"operator_user,omitempty"`
}

type HaulStatusRes struct {
	Code        string `json:"code"`
	Label       string `json:"label"`
	Description string `json:"description"`
}

type HaulSubStatusRes struct {
	Code           string    `json:"code"`
	Label          string    `json:"label"`
	Description    string    `json:"description"`
	MainStatusCode string    `json:"main_status_code"`
	IsLoad         null.Bool `json:"is_load"`
}

type AssetBasicRes struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	SerialNumber string `json:"serial_number"`
}

type UserBasicRes struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email"`
}

// Status DTOs
type HaulStatusListRes struct {
	HaulStatuses []HaulStatusWithSubStatusRes `json:"haul_statuses"`
}

type HaulStatusWithSubStatusRes struct {
	Code            string              `json:"code"`
	Label           string              `json:"label"`
	Description     string              `json:"description"`
	HaulSubStatuses []HaulSubStatusRes  `json:"haul_sub_statuses"`
}

// Statistics DTOs
type HaulingStatsRes struct {
	TotalHaulings     int64 `json:"total_haulings"`
	ActiveHaulings    int64 `json:"active_haulings"`
	CompletedHaulings int64 `json:"completed_haulings"`
	ByStatus          []HaulingStatusStatsRes `json:"by_status"`
}

type HaulingStatusStatsRes struct {
	StatusCode  string `json:"status_code"`
	StatusLabel string `json:"status_label"`
	Count       int64  `json:"count"`
}
